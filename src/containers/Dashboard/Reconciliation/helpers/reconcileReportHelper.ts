import { ComparisonKeyMappingType, PrimaryKeyMappingType } from '+types';
import { capitalizeRemovedash } from '+utils';

export const buildReconciliationReportOptions = (
  primaryKeyMappings: PrimaryKeyMappingType,
  comparisonKeyMappings: ComparisonKeyMappingType
) => {
  const allProcessorReportOptions = primaryKeyMappings.map(item => ({
    label: capitalizeRemovedash(item.processor_report),
    value: item.processor_report,
    color: item.color
  }));
  const allInternalReportOptions = primaryKeyMappings.map(item => ({
    label: capitalizeRemovedash(item.internal_report),
    value: item.internal_report,
    color: item.color
  }));

  const processorReportOptions =
    comparisonKeyMappings.length === 0
      ? allProcessorReportOptions
      : allProcessorReportOptions.filter(item => comparisonKeyMappings.every(option => option?.processor_report !== item.value));

  const internalReportOptions =
    comparisonKeyMappings.length === 0
      ? allInternalReportOptions
      : allInternalReportOptions.filter(item => comparisonKeyMappings.every(option => option?.internal_report !== item.value));
  return [processorReportOptions, internalReportOptions];
};

// Array of accessible, high-contrast colors
const ACCESSIBLE_COLORS = [
  '#2E7D32', // Green
  '#1565C0', // Blue
  '#6A1B9A', // Purple
  '#D84315', // Orange
  '#C62828', // Red
  '#00695C', // Teal
  '#4527A0', // Deep Purple
  '#283593', // Indigo
  '#00838F', // Cyan
  '#558B2F', // Light Green
  '#EF6C00', // Orange
  '#6D4C41', // Brown
  '#F9A825', // Yellow
  '#AD1457', // Pink
  '#0277BD' // Light Blue
];

/**
 * Returns an accessible color from a predefined array based on the index
 * @param {number} index - The index to use for color selection
 * @returns {string} A color hex code from the ACCESSIBLE_COLORS array
 */
export const generateColor = (index = 0) => {
  return ACCESSIBLE_COLORS[index % ACCESSIBLE_COLORS.length];
};
